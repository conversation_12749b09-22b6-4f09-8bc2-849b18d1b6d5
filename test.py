import os
import torch
import numpy as np
import time
from env.ieee30_env import IEEE30Env
from agents.gnn.gnn_ddpg import GNNDDPGAgent
from agents.gnn.graph_utils import IEEE30GraphBuilder, state_to_graph
from utils.common import plot_training_curves, load_model
from config import ENV_CONFIG, AGENT_CONFIG, TEST_CONFIG, MODEL_PATHS, PLOT_CONFIG, DEVICE, VERBOSE_OUTPUT

def test():
    print(f"使用设备: {DEVICE}")
    start_time = time.time()

    # 1. 实例化环境
    env = IEEE30Env(**ENV_CONFIG)

    # 计算状态和动作维度
    state = env.reset()
    state_dim = len(state)  # 动态获取状态维度
    action_dim = 5 * 16

    # 在详细模式下显示维度信息
    if VERBOSE_OUTPUT:
        print(f"状态维度: {state_dim}")
        print(f"动作维度: {action_dim}")

    # 2. 初始化图构建器
    graph_builder = IEEE30GraphBuilder(
        num_buses=30, 
        num_branches=41, 
        num_gens=6
    )
    
    # 定义节点和边的特征维度
    calculated_node_features = state_dim // 16  # 动态计算节点特征维度，这是每个时间步的状态向量长度
    edge_features = 1  # 支路潮流
    
    # 在详细模式下显示节点特征维度信息
    if VERBOSE_OUTPUT:
        print(f"状态维度: {state_dim}")
        print(f"计算得到的每时间步特征维度: {calculated_node_features}")
        print(f"实际节点特征维度: 3")
    
    # 初始化GNN-DDPG智能体
    agent = GNNDDPGAgent(
        node_features=3,  # 使用实际的节点特征维度(负荷、是否有发电机、发电机出力)
        edge_features=edge_features,
        action_dim=action_dim,
        action_bound=1.0,
        time_steps=16,
        hidden_dim=AGENT_CONFIG['hidden_size'] // 8,  # 减小隐藏层维度以适应GNN
        config=AGENT_CONFIG
    )

    # 加载训练好的模型权重
    if not load_model(agent, MODEL_PATHS['model_dir'], load_best=True):
        print("错误: 未找到GNN-DDPG模型权重文件")
        return
    else:
        print("已成功加载模型权重")

    # 3. 测试回合
    test_reward_history = []
    test_actor_loss_history = []
    test_critic_loss_history = []
    
    print("\n开始测试...", flush=True)
    print("-" * 50, flush=True)

    avg_reward = 0.0
    for ep in range(TEST_CONFIG['num_episodes']):
        state = env.reset()
        total_reward = 0.0
        episode_actor_loss = 0.0
        episode_critic_loss = 0.0
        done = False
        steps = 0

        while not done:
            # 测试时不添加噪声
            action = agent.select_action(state, graph_builder, add_noise=False)

            next_state, reward, done, info = env.step(action)
            if next_state is None:
                next_state = state

            state = next_state
            total_reward += reward
            steps += 1
            
            # 在详细模式下显示每个步骤的奖励
            if VERBOSE_OUTPUT and steps % 5 == 0:  # 每5步显示一次，避免输出过多
                print(f"步骤 {steps}: 奖励 {reward:.4f}")
                if info is not None:
                    print(f"  发电成本: {info['gen_cost']:.2f} | 成本奖励: {info['gen_cost_reward']:.2f}")
                    print(f"  功率不平衡: {info['power_imbalance']:.2f} | 风电利用: {info['wind_power']:.2f} MW")

        test_reward_history.append(total_reward)
        test_actor_loss_history.append(episode_actor_loss)
        test_critic_loss_history.append(episode_critic_loss)
        avg_reward += total_reward
        
        print(f"测试回合 {ep + 1}/{TEST_CONFIG['num_episodes']} | 奖励: {total_reward:.2f} | 步数: {steps}")
        
        # 在详细模式下显示额外信息
        if VERBOSE_OUTPUT and info is not None:
            print(f"最终状态详情:")
            print(f"  发电成本: {info['gen_cost']:.2f} | 成本奖励: {info['gen_cost_reward']:.2f}")
            print(f"  发电机违约: {info['gen_violation']:.2f} | 功率不平衡: {info['power_imbalance']:.2f}")
            print(f"  线路违约: {info['line_violation']:.2f} | 爬坡违约: {info['ramp_violation']:.2f}")
            print(f"  风电利用: {info['wind_power']:.2f} MW | 奖励: {info['wind_utilization_reward']:.2f}")
            print(f"  潮流计算: {'收敛' if info['convergence_success'] else '不收敛'}")
            print()

    avg_reward /= TEST_CONFIG['num_episodes']
    print("-" * 50, flush=True)
    print(f"平均测试奖励: {avg_reward:.2f}", flush=True)
    
    # 4. 绘制测试曲线
    plot_training_curves(
        test_reward_history,
        test_actor_loss_history,
        test_critic_loss_history,
        None,  # 不再需要supervision_weight_history
        PLOT_CONFIG['output_dir']
    )

    end_time = time.time()
    print(f"测试总耗时: {(end_time - start_time) / 3600:.2f} 小时", flush=True)

if __name__ == "__main__":
    test()
