import numpy as np
import matplotlib.pyplot as plt
import os
import torch
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from config import DEVICE

class RunningMeanStd:
    """计算运行时的均值和标准差"""
    def __init__(self, epsilon=1e-4, shape=()):
        self.mean = np.zeros(shape, 'float64')
        self.var = np.ones(shape, 'float64')
        self.count = epsilon

    def update(self, x):
        batch_mean = np.mean(x, axis=0)
        batch_var = np.var(x, axis=0)
        batch_count = x.shape[0]
        self.update_from_moments(batch_mean, batch_var, batch_count)

    def update_from_moments(self, batch_mean, batch_var, batch_count):
        delta = batch_mean - self.mean
        tot_count = self.count + batch_count
        new_mean = self.mean + delta * batch_count / tot_count
        m_a = self.var * self.count
        m_b = batch_var * batch_count
        M2 = m_a + m_b + np.square(delta) * self.count * batch_count / tot_count
        new_var = M2 / tot_count
        self.mean = new_mean
        self.var = new_var
        self.count = tot_count

class RewardNormalizer:
    """奖励归一化类，用于归一化奖励值并记录统计信息"""
    def __init__(self, device=torch.device('cpu')):
        self.device = device
        
        # 重新设定基准值：保持你的动态更新逻辑，这是合理的
        self.base_values = {
            'gen_cost': 300.0,            # 发电成本基准值（由optimal_cost动态更新）
            'power_balance': 10.0,        # 功率不平衡基准值（已取消使用）
            'branch_violation': 2.0,      # 线路过载基准值（适当提高）
            'gen_violation': 2.0,         # 爬坡限制基准值（适当提高）
            'gen_limit': 2.0,             # 发电机越限基准值（适当提高）
            'wind_curtailment': 50.0      # 弃风惩罚基准值（已取消使用）
        }
        
        # === 重新平衡组件权重：突出发电成本，平衡约束惩罚 ===
        # 理由：发电成本是主要优化目标，约束违反是安全底线
        self.weights = {
            'gen_cost': 2.0,           # 发电成本权重（主要优化目标）
            'power_balance': 0.5,       # 功率不平衡权重（包含风电）
            'branch_violation': 0.5,   # 线路过载权重（安全约束）
            'gen_violation': 0.30,      # 爬坡限制权重（运行约束）
            'gen_limit': 0.20,          # 发电机越限权重（设备约束）
            'wind_limit': 0.20,         # 风电出力限制权重（降低权重）
            'spinning_reserve': 0.25,   # 旋转备用权重（降低权重）
            'wind_ramp': 0.15,          # 风电爬坡权重（降低权重）
        }
        
        # 统计信息
        self.stats = {
            'original': {
                'values': [],
                'min': float('inf'),
                'max': float('-inf'),
                'mean': 0,
                'std': 0
            },
            'normalized': {
                'values': [],
                'min': float('inf'),
                'max': float('-inf'),
                'mean': 0,
                'std': 0
            },
            'components': {comp: {'values': []} for comp in self.base_values.keys()}
        }
        
        # 最佳奖励记录（使用原始值）
        self.best_reward = float('-inf')
        self.best_normalized_reward = float('-inf')
    
    def get_normalized_components(self):
        """获取最近一次计算的归一化组件值"""
        return getattr(self, 'last_normalized_components', {})
    
    def normalize(self, raw_reward, components=None, update_stats=True):
        """
        归一化原始奖励值
        
        Args:
            raw_reward: 原始奖励值
            components: 奖励组件字典，包含各个组件的原始值
            update_stats: 是否更新统计信息
            
        Returns:
            normalized_reward: 归一化后的奖励值
        """
        # 确保张量在正确的设备上
        if isinstance(raw_reward, torch.Tensor):
            raw_reward = raw_reward.to(self.device)
        else:
            raw_reward = torch.tensor(raw_reward, dtype=torch.float32, device=self.device)
        
        # 1. 基于组件的归一化
        norm_reward = 0.0
        
        if components is not None:
            normalized_components = {}
            
            # 对每个组件进行归一化
            for comp_name, base_value in self.base_values.items():
                if comp_name in components:
                    raw_value = components[comp_name]
                    
                    # 转换为张量
                    if not isinstance(raw_value, torch.Tensor):
                        raw_value = torch.tensor(raw_value, dtype=torch.float32, device=self.device)
                    
                    # 使用线性缩放进行归一化，并裁剪到 [-5, 0]
                    # 在放大梯度的同时避免过度放大极端值
                    normalized_value = torch.clamp(raw_value / base_value, -5.0, 0.0)
                    
                    # 应用权重
                    weighted_value = normalized_value * self.weights[comp_name]
                    norm_reward += weighted_value
                    
                    # 记录归一化后的组件值
                    normalized_components[comp_name] = normalized_value.item()
                    
                    # 更新组件统计信息
                    if update_stats:
                        self.stats['components'][comp_name]['values'].append(raw_value.item())
            
            # 保存最新的归一化组件，以便外部访问
            self.last_normalized_components = normalized_components
            
            # 更新统计信息
            if update_stats:
                # 更新原始奖励统计
                raw_reward_value = raw_reward.item()
                self.stats['original']['values'].append(raw_reward_value)
                self.stats['original']['min'] = min(self.stats['original']['min'], raw_reward_value)
                self.stats['original']['max'] = max(self.stats['original']['max'], raw_reward_value)
                
                # 更新归一化奖励统计
                norm_reward_value = norm_reward.item()
                self.stats['normalized']['values'].append(norm_reward_value)
                self.stats['normalized']['min'] = min(self.stats['normalized']['min'], norm_reward_value)
                self.stats['normalized']['max'] = max(self.stats['normalized']['max'], norm_reward_value)
                
                # 更新均值和标准差
                if len(self.stats['original']['values']) > 0:
                    self.stats['original']['mean'] = np.mean(self.stats['original']['values'])
                    self.stats['original']['std'] = np.std(self.stats['original']['values'])
                    self.stats['normalized']['mean'] = np.mean(self.stats['normalized']['values'])
                    self.stats['normalized']['std'] = np.std(self.stats['normalized']['values'])
                
                # 检查是否是最佳奖励
                if raw_reward_value > self.best_reward:
                    self.best_reward = raw_reward_value
                if norm_reward_value > self.best_normalized_reward:
                    self.best_normalized_reward = norm_reward_value
        else:
            # 如果没有提供组件，则使用简单的归一化方法
            # 线性缩放替代Sigmoid函数
            norm_reward = torch.clamp(raw_reward / 10000.0, -1.0, 0.0)
            
            if update_stats:
                self.stats['original']['values'].append(raw_reward.item())
                self.stats['normalized']['values'].append(norm_reward.item())
        
        return norm_reward
    
    def update_base_values(self, new_base_values):
        """更新基准值"""
        for key, value in new_base_values.items():
            if key in self.base_values:
                self.base_values[key] = value
    
    def update_weights(self, new_weights):
        """更新权重"""
        for key, value in new_weights.items():
            if key in self.weights:
                self.weights[key] = value
    
    def get_stats(self):
        """获取统计信息"""
        return self.stats
    
    def get_normalized_reward_range(self):
        """获取归一化奖励的范围"""
        return (self.stats['normalized']['min'], self.stats['normalized']['max'])
    
    def is_best_reward(self, raw_reward):
        """检查是否是最佳奖励（使用原始值比较）"""
        if isinstance(raw_reward, torch.Tensor):
            raw_reward = raw_reward.item()
        
        # 针对负值奖励进行适当的比较（更大的值/更接近0的值为更好的奖励）
        if self.best_reward == float('-inf'):
            # 首次记录奖励
            print(f"初始化最佳奖励: {raw_reward:.2f}")
            self.best_reward = raw_reward
            return True
        
        # 考虑到我们的奖励是负值，值越大越好
        is_better = raw_reward > self.best_reward
        if is_better:
            # 打印详细的比较信息
            print(f"发现新的最佳奖励: {raw_reward:.2f}")
            self.best_reward = raw_reward
        
        return is_better

def plot_training_curves(total_penalty, actor_loss_history, critic_loss_history, 
                         output_dir, rewards_penalties_data=None):
    """绘制训练过程中的各种曲线"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 确保数据在CPU上且为NumPy数组
    # 处理total_penalty
    if isinstance(total_penalty, torch.Tensor):
        total_penalty = total_penalty.cpu().numpy()
    elif isinstance(total_penalty, list):
        # 检查列表中的每个元素是否为张量
        for i in range(len(total_penalty)):
            if isinstance(total_penalty[i], torch.Tensor):
                total_penalty[i] = total_penalty[i].cpu().numpy()
    
    # 处理actor_loss_history
    if isinstance(actor_loss_history, torch.Tensor):
        actor_loss_history = actor_loss_history.cpu().numpy()  
    elif isinstance(actor_loss_history, list):
        # 检查列表中的每个元素是否为张量
        for i in range(len(actor_loss_history)):
            if isinstance(actor_loss_history[i], torch.Tensor):
                actor_loss_history[i] = actor_loss_history[i].cpu().numpy()
    
    # 处理critic_loss_history
    if isinstance(critic_loss_history, torch.Tensor):
        critic_loss_history = critic_loss_history.cpu().numpy()
    elif isinstance(critic_loss_history, list):
        # 检查列表中的每个元素是否为张量
        for i in range(len(critic_loss_history)):
            if isinstance(critic_loss_history[i], torch.Tensor):
                critic_loss_history[i] = critic_loss_history[i].cpu().numpy()
    
    # 将所有数据转换为numpy数组以确保兼容性
    total_penalty = np.array(total_penalty)
    actor_loss_history = np.array(actor_loss_history)
    critic_loss_history = np.array(critic_loss_history)
    
    episodes = len(total_penalty)
    
    # 设置matplotlib支持中文
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'NSimSun', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 绘制总奖励曲线
    plt.figure(figsize=(14, 7))
    plt.plot(range(1, episodes + 1), total_penalty)
    plt.title('总奖励曲线')
    plt.xlabel('回合数')
    plt.ylabel('奖励')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'Total_Reward_Curve.png'))
    plt.close()
    
    # 绘制Actor-Critic损失曲线
    plt.figure(figsize=(14, 7))
    plt.plot(range(1, len(actor_loss_history) + 1), actor_loss_history, label='Actor总损失')
    plt.plot(range(1, len(critic_loss_history) + 1), critic_loss_history, label='Critic总损失')
    plt.title('Actor-Critic总损失曲线')
    plt.xlabel('回合数')
    plt.ylabel('损失')
    plt.legend(prop={'family': 'SimHei'})  # 设置图例字体
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'Actor_Critic_Loss_Curves.png'))
    plt.close()
    
    # 如果提供了rewards_penalties_data，绘制归一化奖励曲线和惩罚组件
    if rewards_penalties_data is not None:
        # 提取归一化奖励数据
        normalized_reward = rewards_penalties_data.get('normalized_reward', None)
        if normalized_reward is not None:
            plt.figure(figsize=(14, 7))
            plt.plot(range(1, len(normalized_reward) + 1), normalized_reward)
            plt.title('归一化奖励曲线')
            plt.xlabel('回合数')
            plt.ylabel('归一化奖励')
            plt.grid(True)
            plt.savefig(os.path.join(output_dir, 'Normalized_Reward_Curve.png'))
            plt.close()

def plot_supervision_curves(supervision_loss_history, policy_loss_history, 
                           supervision_weight_history, output_dir):
    """绘制监督学习相关的曲线"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 确保数据在CPU上且为NumPy数组
    if isinstance(supervision_loss_history, torch.Tensor):
        supervision_loss_history = supervision_loss_history.cpu().numpy()
    
    if isinstance(policy_loss_history, torch.Tensor):
        policy_loss_history = policy_loss_history.cpu().numpy()
    
    if isinstance(supervision_weight_history, torch.Tensor):
        supervision_weight_history = supervision_weight_history.cpu().numpy()
    
    # 设置matplotlib支持中文
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'NSimSun', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    # 创建两个子图，分别显示监督损失和策略损失
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12), sharex=True)
    
    # 获取数据点数量
    num_episodes = len(supervision_loss_history)
    
    # 第一个子图 - 监督损失
    ax1.plot(range(1, num_episodes + 1), supervision_loss_history, 'b-', label='监督损失')
    ax1.set_ylabel('监督损失', color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')
    ax1.set_title(f'监督学习损失曲线 (共{num_episodes}回合)')
    ax1.grid(True)
    ax1.legend(loc='upper right', prop={'family': 'SimHei'})
    
    # 添加移动平均线以显示趋势
    window_size = min(50, num_episodes // 10) if num_episodes > 50 else 5
    if window_size > 1:
        supervision_ma = np.convolve(supervision_loss_history, np.ones(window_size)/window_size, mode='valid')
        ma_x = range(window_size, num_episodes + 1)
        ax1.plot(ma_x, supervision_ma, 'r--', linewidth=1.5, label=f'{window_size}回合移动平均')
    ax1.legend(loc='upper right', prop={'family': 'SimHei'})
    
    # 第二个子图 - 策略损失
    if isinstance(policy_loss_history, np.ndarray) and policy_loss_history.size > 0:
        ax2.plot(range(1, len(policy_loss_history) + 1), policy_loss_history, 'orange', label='策略损失')
        ax2.set_ylabel('策略损失', color='orange')
        ax2.tick_params(axis='y', labelcolor='orange')
        ax2.set_title(f'策略学习损失曲线 (共{len(policy_loss_history)}回合)')
        ax2.grid(True)
        ax2.legend(loc='upper right', prop={'family': 'SimHei'})
        
        # 添加移动平均线
        if window_size > 1:
            policy_ma = np.convolve(policy_loss_history, np.ones(window_size)/window_size, mode='valid')
            ax2.plot(ma_x, policy_ma, 'r--', linewidth=1.5, label=f'{window_size}回合移动平均')
        ax2.legend(loc='upper right', prop={'family': 'SimHei'})
    
    # 共享的x轴标签
    ax2.set_xlabel('回合数')
    
    # 调整子图之间的间距
    plt.tight_layout()
    
    # 设置总标题
    fig.suptitle(f'监督学习和策略学习损失曲线 (总计{num_episodes}回合)', fontsize=16, y=0.98)
    plt.subplots_adjust(top=0.9)
    
    # 保存图表
    plt.savefig(os.path.join(output_dir, 'Supervision_Policy_Loss.png'))
    plt.close()
    
    # 绘制监督权重变化曲线
    plt.figure(figsize=(14, 7))
    plt.plot(range(1, len(supervision_weight_history) + 1), supervision_weight_history)
    plt.title(f'监督学习权重变化曲线 (共{len(supervision_weight_history)}回合)')
    plt.xlabel('回合数')
    plt.ylabel('监督权重')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'Supervision_Weight_Curve.png'))
    plt.close()

def save_model(agent, model_dir, is_best=False):
    """保存模型
    支持传统DDPG和基于GNN的DDPG模型
    """
    os.makedirs(model_dir, exist_ok=True)
    
    # 检查是否为GNN-DDPG模型 - 通过检查actor属性
    is_gnn = hasattr(agent.actor, 'encoder')
    
    if is_gnn:
        # 保存GNN-DDPG模型
        if is_best:
            torch.save({
                'actor_state_dict': agent.actor.state_dict(),
                'critic_state_dict': agent.critic.state_dict(),
                'model_type': 'gnn_ddpg',
                'node_features': agent.node_features,
                'edge_features': agent.edge_features,
                'hidden_dim': agent.hidden_dim,
                'time_steps': agent.time_steps,
                'episodes_trained': agent.episodes_trained,  # 保存训练回合数
                'best_reward': agent.best_reward,  # 保存最佳奖励
                'best_reward_episode': agent.best_reward_episode  # 保存最佳奖励对应的回合
            }, os.path.join(model_dir, 'best_gnn_model.pth'))
        else:
            torch.save({
                'actor_state_dict': agent.actor.state_dict(),
                'critic_state_dict': agent.critic.state_dict(),
                'model_type': 'gnn_ddpg',
                'node_features': agent.node_features,
                'edge_features': agent.edge_features,
                'hidden_dim': agent.hidden_dim,
                'time_steps': agent.time_steps,
                'episodes_trained': agent.episodes_trained,  # 保存训练回合数
                'best_reward': agent.best_reward,  # 保存最佳奖励
                'best_reward_episode': agent.best_reward_episode  # 保存最佳奖励对应的回合
            }, os.path.join(model_dir, 'ddpg_gnn_model.pth'))
        
        print(f"已保存GNN-DDPG模型 {'(最佳)' if is_best else ''}, 已训练回合数: {agent.episodes_trained}")
    else:
        # 保存传统DDPG模型
        if is_best:
            torch.save(agent.actor.state_dict(), os.path.join(model_dir, 'best_actor.pth'))
            torch.save(agent.critic.state_dict(), os.path.join(model_dir, 'best_critic.pth'))
        else:
            torch.save(agent.actor.state_dict(), os.path.join(model_dir, 'ddpg_actor.pth'))
            torch.save(agent.critic.state_dict(), os.path.join(model_dir, 'ddpg_critic.pth'))
        
        print(f"已保存DDPG模型 {'(最佳)' if is_best else ''}")

def load_model(agent, model_dir, load_best=False):
    """加载模型
    支持传统DDPG和基于GNN的DDPG模型
    """
    # 检查是否为GNN-DDPG模型 - 通过检查actor属性
    is_gnn = hasattr(agent.actor, 'encoder')
    
    if is_gnn:
        # 加载GNN-DDPG模型
        model_path = os.path.join(model_dir, 'best_gnn_model.pth' if load_best else 'ddpg_gnn_model.pth')
        
        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location=DEVICE)
            
            # 验证模型类型
            if checkpoint.get('model_type') == 'gnn_ddpg':
                # 验证模型结构参数
                if (checkpoint.get('node_features') == agent.node_features and
                    checkpoint.get('edge_features') == agent.edge_features and
                    checkpoint.get('hidden_dim') == agent.hidden_dim and
                    checkpoint.get('time_steps') == agent.time_steps):
                    
                    agent.actor.load_state_dict(checkpoint['actor_state_dict'])
                    agent.critic.load_state_dict(checkpoint['critic_state_dict'])
                    
                    # 加载训练回合数和最佳奖励信息
                    if 'episodes_trained' in checkpoint:
                        agent.episodes_trained = checkpoint['episodes_trained']
                        print(f"已加载训练回合数: {agent.episodes_trained}")
                    if 'best_reward' in checkpoint:
                        agent.best_reward = checkpoint['best_reward']
                    if 'best_reward_episode' in checkpoint:
                        agent.best_reward_episode = checkpoint['best_reward_episode']
                    
                    print(f"已加载GNN-DDPG模型 {'(最佳)' if load_best else ''}, 已训练回合数: {agent.episodes_trained}")
                    return True
                else:
                    print("GNN-DDPG模型结构不匹配，无法加载")
                    return False
            else:
                print("模型类型不匹配，期望GNN-DDPG模型")
                return False
        return False
    else:
        # 加载传统DDPG模型
        if load_best:
            actor_path = os.path.join(model_dir, 'best_actor.pth')
            critic_path = os.path.join(model_dir, 'best_critic.pth')
        else:
            actor_path = os.path.join(model_dir, 'ddpg_actor.pth')
            critic_path = os.path.join(model_dir, 'ddpg_critic.pth')
            
        if os.path.exists(actor_path) and os.path.exists(critic_path):
            agent.actor.load_state_dict(torch.load(actor_path, map_location=DEVICE))
            agent.critic.load_state_dict(torch.load(critic_path, map_location=DEVICE))
            print(f"已加载DDPG模型 {'(最佳)' if load_best else ''}")
            return True
        return False