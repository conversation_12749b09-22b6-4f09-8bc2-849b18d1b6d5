import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os

# 读取负荷数据
df = pd.read_csv('./data/active_load_profile.csv')
total_load = df.sum(axis=1)

# 创建时间索引
time_index = pd.date_range("2025-01-01", periods=len(total_load), freq="15T")
total_load.index = time_index

# 计算相邻时间点的变化
changes = total_load.diff().abs()

# 创建图表目录
os.makedirs("./figures", exist_ok=True)

# 绘制一周的负荷曲线
plt.figure(figsize=(15, 6))
# 选择第一周数据
first_week = total_load['2025-01-01':'2025-01-07']
plt.plot(first_week.index, first_week.values, 'b-', linewidth=1.5)
plt.title('一周负荷曲线 (2025-01-01 至 2025-01-07)', fontsize=14)
plt.xlabel('日期', fontsize=12)
plt.ylabel('总负荷 (MW)', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('./figures/week_load_profile.png', dpi=300)

# 绘制一天的负荷曲线
plt.figure(figsize=(12, 5))
# 选择第一天数据
first_day = total_load['2025-01-01']
plt.plot(first_day.index, first_day.values, 'r-', linewidth=1.5)
plt.title('一天负荷曲线 (2025-01-01)', fontsize=14)
plt.xlabel('时间', fontsize=12)
plt.ylabel('总负荷 (MW)', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('./figures/day_load_profile.png', dpi=300)

# 绘制负荷变化直方图
plt.figure(figsize=(10, 5))
plt.hist(changes, bins=50, alpha=0.7, color='green')
plt.title('相邻时间点负荷变化分布', fontsize=14)
plt.xlabel('负荷变化 (MW)', fontsize=12)
plt.ylabel('频次', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.5)
plt.tight_layout()
plt.savefig('./figures/load_changes_histogram.png', dpi=300)

print("图表已保存至 ./figures 目录") 