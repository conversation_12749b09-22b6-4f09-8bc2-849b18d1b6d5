import pandas as pd
import numpy as np
import os
from scipy.ndimage import gaussian_filter1d

# 类型注释，帮助IDE识别模块
# type: ignore
import holidays

# | 调节维度 | 时段划分 | 系数变化 | 作用说明 |
# |----------|-------------------------|----------|---------------------------|
# | 日周期 | 00:00-05:45 | ×0.80 | 反映夜间用电低谷 |
# | | 05:45-08:45 | ×1.20 | 早高峰负荷激增 |
# | | 08:45-17:45 | ×1.00 | 日间基准负荷 |
# | | 17:45-24:00 | ×1.10 | 晚高峰适度提升 |
# | 年周期 | 1月-2月 | ×1.08 | 冬季取暖负荷增加 |
# | | 6月 | ×1.15 | 夏季空调负荷高峰 |
# | 周周期 | 周末 | ×0.85 | 周末负荷降低 |
# | 特殊日期 | 节假日 | ×0.80 | 节假日负荷降低 |
# | 随机波动 | 所有时刻 | ±2% | 模拟不可预测的负荷波动 |
# | 中频波动 | 每3-5天 | ±5% | 模拟天气变化等中期影响 |

# 基准有功负荷
base_load = {
    1: 0.00,
    2: 21.7,
    3: 2.4,
    4: 7.6,
    5: 94.2,
    6: 0.0,
    7: 10.9,
    8: 30.0,
    9: 0.0,
    10: 5.8,
    11: 0.0,
    12: 11.2,
    13: 0.0,
    14: 6.2,
    15: 8.2,
    16: 3.5,
    17: 9.0,
    18: 3.2,
    19: 9.5,
    20: 2.2,
    21: 17.5,
    22: 0.0,
    23: 3.2,
    24: 8.7,
    25: 0.0,
    26: 3.5,
    27: 0.0,
    28: 0.0,
    29: 2.4,
    30: 10.6
}

# 生成时间序列（半年数据，15分钟间隔）
START_DATE = "2025-01-01"
END_DATE = "2025-06-30"
FREQ = "15T"
time_index = pd.date_range(START_DATE, END_DATE, freq=FREQ)

# 创建中国节假日列表
cn_holidays = holidays.CN(years=[2025])

# 生成自相关随机噪声序列
def generate_smooth_noise(length, std_dev=0.02, correlation_steps=4):
    """生成平滑的自相关随机噪声序列"""
    # 生成白噪声
    white_noise = np.random.normal(0, std_dev, length)
    # 应用高斯滤波使噪声平滑
    smooth_noise = gaussian_filter1d(white_noise, sigma=correlation_steps)
    return smooth_noise

# 生成中频波动（模拟天气变化等）
def generate_medium_freq_variations(length, num_cycles=30, amplitude=0.05):
    """生成中频波动，模拟天气变化等中期影响"""
    # 生成基础正弦波
    t = np.linspace(0, 2*np.pi*num_cycles, length)
    # 添加多个不同频率的正弦波
    variations = np.zeros(length)
    for i in range(3, 8):  # 使用不同频率
        phase = np.random.rand() * 2 * np.pi  # 随机相位
        amp = amplitude * (0.5 + 0.5 * np.random.rand())  # 随机振幅
        variations += amp * np.sin(t/i + phase)
    
    # 添加随机扰动
    variations += np.random.normal(0, 0.01, length)
    
    # 平滑处理
    variations = gaussian_filter1d(variations, sigma=8)
    return variations

# 为每个节点生成独立的噪声序列
num_time_points = len(time_index)
noise_dict = {node: generate_smooth_noise(num_time_points) for node in base_load.keys()}

# 生成中频波动（所有节点共享）
medium_freq_variations = generate_medium_freq_variations(num_time_points)

def generate_active_load(timestamp, time_idx):
    hour, minute = timestamp.hour, timestamp.minute
    month = timestamp.month
    weekday = timestamp.weekday()  # 0-6，0是周一
    date_str = timestamp.strftime('%Y-%m-%d')

    # 1. 时段判断
    if (hour < 5) or (hour == 5 and minute < 45):
        factor = 0.80
    elif (5 <= hour < 8) or (hour == 8 and minute < 45):
        factor = 1.20
    elif (8 <= hour < 17) or (hour == 17 and minute < 45):
        factor = 1.00
    else:
        factor = 1.10
    
    # 2. 季节调整
    if month in [1, 2]: 
        factor *= 1.08  # 冬季
    elif month == 6: 
        factor *= 1.15  # 夏季
    
    # 3. 周末调整
    if weekday >= 5:  # 周六和周日
        factor *= 0.85
    
    # 4. 节假日调整
    if date_str in cn_holidays:
        factor *= 0.80
    
    # 5. 中频波动（天气等影响）
    medium_freq_factor = 1.0 + medium_freq_variations[time_idx]
    
    # 6. 使用预生成的平滑噪声
    result = {}
    for node, base in base_load.items():
        # 获取当前时间点的噪声值
        noise = noise_dict[node][time_idx]
        # 7. 全局缩放系数(确保负荷总和小于335MW)
        global_scaling = 0.825
        
        # 计算最终负荷值
        load_value = base * factor * medium_freq_factor * (1 + noise) * global_scaling
        result[node] = round(load_value, 2)
    
    return result

# 生成原始数据
raw_data = []
for idx, t in enumerate(time_index):
    raw_data.append(generate_active_load(t, idx))

# 生成数据框
df = pd.DataFrame(raw_data)

# 对每列数据应用滑动平均进一步平滑，但使用较小的窗口
window_size = 3  # 45分钟窗口(3个15分钟间隔)
for col in df.columns:
    if base_load[int(col)] > 0:  # 只对非零基准负荷的节点应用平滑
        df[col] = df[col].rolling(window=window_size, center=True, min_periods=1).mean()

# 修改列名格式为 bus.x.ld
df.columns = [f"bus.{node}.ld" for node in base_load.keys()]

# 修改存储路径到data目录
output_path = "./data/active_load_profile.csv"
# 创建输出目录（如果不存在）
os.makedirs(os.path.dirname(output_path), exist_ok=True)

# 保存数据（CSV格式）
df.to_csv(output_path, index=False)

print("已生成更加丰富多样的负荷数据，保存至", output_path)